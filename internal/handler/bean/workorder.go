package bean

// WorkorderGetTicketTemplateReq 获取工单模板请求
type WorkorderGetTicketTemplateReq struct {
	GameID string `json:"game_id" form:"game_id"` // 游戏ID，非必传
}

// WorkorderGetTicketTemplateResp 获取工单模板响应
type WorkorderGetTicketTemplateResp struct {
	Fields []*WorkorderTemplateField `json:"fields"` // 模板字段列表
}

// WorkorderTemplateField 工单模板字段
type WorkorderTemplateField struct {
	FieldKey    string `json:"field_key"`    // 字段键名
	DisplayName string `json:"display_name"` // 显示名称
	FieldType   string `json:"field_type"`   // 字段类型: string, number, boolean
	IsVisible   bool   `json:"is_visible"`   // toC 是否可见
	Required    bool   `json:"required"`     // 是否必填: 0-否, 1-是
	SortOrder   int32  `json:"sort_order"`   // 排序顺序
}
