package handler

import (
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_workorderOnce    sync.Once
	_workorderHandler *WorkorderHandler
)

type WorkorderHandler struct {
	middleware.BaseHandler
	workorderLogic *logic.WorkorderLogic
}

func NewWorkorderHandler() *WorkorderHandler {
	return &WorkorderHandler{
		workorderLogic: logic.SingletonWorkorderLogic(),
	}
}

// SingletonWorkorderHandler 获取WorkorderHandler单例
func SingletonWorkorderHandler() *WorkorderHandler {
	_workorderOnce.Do(func() {
		_workorderHandler = NewWorkorderHandler()
	})
	return _workorderHandler
}

// QiyuAuthLogin 工单系统登录
func (h *WorkorderHandler) AuthLogin(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.QiyuAuthLoginReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.workorderLogic.AuthLogin(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	c.Header(constants.HeaderAuthKey, resp.Token)
	h.Success(c, resp)
}

// CreateSupportTicket 创建工单
func (h *WorkorderHandler) CreateSupportTicket(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderCreateSupportTicketReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// DeviceID验证：检查是否为空或只包含空格
	if req.DeviceID == "" || len(strings.TrimSpace(req.DeviceID)) == 0 {
		logger.Logger.WarnfCtx(ctx, "创建工单失败: DeviceID验证失败, device_id='%s'", req.DeviceID)
		h.AuthFailed(c, constants.ErrDeviceIDIsEmpty.Error())
		return
	}

	logger.Logger.DebugfCtx(ctx, "创建工单: DeviceID验证通过, device_id='%s'", req.DeviceID)

	err := h.workorderLogic.CreateSupportTicket(ctx, req)
	if err != nil {
		logger.Logger.Errorf("创建工单失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}

// // GetTicketLogs 获取工单操作日志
// func (h *WorkorderHandler) GetTicketLogs(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	req := &bean.WorkorderGetTicketLogsReq{}
// 	if !h.Bind(c, req, true) {
// 		return
// 	}

// 	resp, err := h.workorderLogic.GetTicketLogs(ctx, req)
// 	if err != nil {
// 		logger.Logger.Errorf("获取工单日志失败: %v", err)
// 		h.Fail(c, err)
// 		return
// 	}

// 	h.Success(c, resp)
// }

// GetSupportTickets 获取工单列表
func (h *WorkorderHandler) GetSupportTickets(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderGetSupportTicketsReq{}
	if !h.Bind(c, req, true) {
		return
	}

	// DeviceID验证：检查是否为空或只包含空格
	if req.DeviceID == "" || len(strings.TrimSpace(req.DeviceID)) == 0 {
		logger.Logger.WarnfCtx(ctx, "获取工单列表失败: DeviceID验证失败, device_id='%s'", req.DeviceID)
		h.AuthFailed(c, constants.ErrDeviceIDIsEmpty.Error())
		return
	}

	logger.Logger.DebugfCtx(ctx, "获取工单列表: DeviceID验证通过, device_id='%s'", req.DeviceID)

	resp, err := h.workorderLogic.GetSupportTickets(ctx, req)
	if err != nil {
		logger.Logger.Errorf("获取工单列表失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetTicketDetail 获取工单详情
func (h *WorkorderHandler) GetTicketDetail(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderGetTicketDetailReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.workorderLogic.GetTicketDetail(ctx, req)
	if err != nil {
		logger.Logger.Errorf("获取工单详情失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// UploadFile 上传文件
func (h *WorkorderHandler) UploadFile(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取文件
	file, err := c.FormFile("file")
	if err != nil {
		logger.Logger.Errorf("获取文件失败: %v", err)
		h.Fail(c, err)
		return
	}

	fileURL, err := h.workorderLogic.UploadFile(ctx, file)
	if err != nil {
		logger.Logger.Errorf("上传文件失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, fileURL)
}

// ReplyTicket 回复工单
func (h *WorkorderHandler) ReplyTicket(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderReplyTicketReq{}
	if !h.Bind(c, req, true) {
		return
	}

	err := h.workorderLogic.ReplyTicket(ctx, req)
	if err != nil {
		logger.Logger.Errorf("回复工单失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}

// GetOngoingWorkorderStatus 获取当前用户是否有正在进行中的工单状态
func (h *WorkorderHandler) GetOngoingWorkorderStatus(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOngoingWorkorderStatusReq{} // 初始化请求结构体

	// 定义临时结构体获取查询参数中的game_id
	type GameIDQuery struct {
		GameID string `form:"game_id" binding:"required"`
	}
	var query GameIDQuery

	// 从查询参数中获取game_id
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取game_id参数失败: %v", err)
		h.BadRequest(c, "game_id参数缺失或无效")
		return
	}

	// 定义临时结构体来绑定请求头信息
	type HeaderInfo struct {
		UserID       string `header:"user_id"`
		DeviceID     string `header:"device_id"`
		AppID        string `header:"app_id"`
		OpenID       string `header:"open_id"`
		RefreshToken string `header:"Refresh-Token"`
		OS           string `header:"os"`
	}

	// 绑定请求头信息
	var headerInfo HeaderInfo
	if err := c.ShouldBindHeader(&headerInfo); err != nil {
		logger.Logger.ErrorfCtx(ctx, "绑定请求头信息失败: %v", err)
		h.BadRequest(c, "请求头信息无效")
		return
	}

	// 手动设置请求结构体字段
	req.UserID = headerInfo.UserID
	req.DeviceID = headerInfo.DeviceID
	req.AppID = headerInfo.AppID
	req.OpenID = headerInfo.OpenID
	req.RefreshToken = headerInfo.RefreshToken
	req.OS = headerInfo.OS

	// 使用查询参数中的game_id
	req.GameID = query.GameID

	logger.Logger.InfofCtx(ctx, "[工单系统] 获取用户进行中工单状态, 使用查询参数的GameID: %s, AppID(UnionID): %s", req.GameID, req.AppID)

	resp, err := h.workorderLogic.GetOngoingWorkorderStatus(ctx, req) // Pass the request struct
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取进行中工单状态失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetGameList 获取客服中心游戏列表
func (h *WorkorderHandler) GetGameList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderGameListReq{}
	if !h.Bind(c, req) {
		return
	}
	// 获取游戏列表
	resp, err := h.workorderLogic.GetGameList(ctx)
	if err != nil {
		logger.Logger.Errorf("获取客服中心游戏列表失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetWelcomeContent 获取客服中心欢迎语
func (h *WorkorderHandler) GetWelcomeContent(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderWelcomeReq{}

	// 定义临时结构体获取查询参数中的game_id
	type GameIDQuery struct {
		GameID string `form:"game_id" binding:"required"`
	}
	var query GameIDQuery

	// 从查询参数中获取game_id
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Logger.Errorf("获取game_id参数失败: %v", err)
		h.BadRequest(c, "game_id参数缺失或无效")
		return
	}

	// 定义临时结构体来绑定请求头信息
	type HeaderInfo struct {
		UserID       string `header:"user_id"`
		DeviceID     string `header:"device_id"`
		AppID        string `header:"app_id"`
		OpenID       string `header:"open_id"`
		RefreshToken string `header:"Refresh-Token"`
		OS           string `header:"os"`
	}

	// 绑定请求头信息
	var headerInfo HeaderInfo
	if err := c.ShouldBindHeader(&headerInfo); err != nil {
		logger.Logger.Errorf("绑定请求头信息失败: %v", err)
		h.BadRequest(c, "请求头信息无效")
		return
	}

	// 手动设置请求结构体字段
	req.UserID = headerInfo.UserID
	req.DeviceID = headerInfo.DeviceID
	req.AppID = headerInfo.AppID
	req.OpenID = headerInfo.OpenID
	req.RefreshToken = headerInfo.RefreshToken
	req.OS = headerInfo.OS

	// 使用查询参数中的game_id
	req.Header.GameID = query.GameID

	logger.Logger.InfofCtx(ctx, "[工单系统] 获取客服中心欢迎语, 使用查询参数的GameID: %s, AppID(UnionID): %s", req.GameID, req.AppID)

	resp, err := h.workorderLogic.GetWelcomeContent(ctx, req)
	if err != nil {
		logger.Logger.Errorf("获取客服中心欢迎语失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetBusySwitch 获取繁忙提示开关状态
func (h *WorkorderHandler) GetBusySwitch(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderBusySwitchReq{}
	if !h.Bind(c, req) {
		return
	}

	resp, err := h.workorderLogic.GetBusySwitch(ctx, req)
	if err != nil {
		logger.Logger.Errorf("获取繁忙提示开关状态失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// ManualTriggerWorkorderStats 手动触发工单统计邮件发送
func (h *WorkorderHandler) ManualTriggerWorkorderStats(c *gin.Context) {
	ctx := c.Request.Context()

	// 调用Logic层
	resp, err := h.workorderLogic.ManualTriggerWorkorderStats(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
