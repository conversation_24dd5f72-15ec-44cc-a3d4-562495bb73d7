package service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gen"
	"gorm.io/gorm"
)

var (
	_workorderOnce    sync.Once
	_workorderService *WorkorderService
)

type WorkorderService struct {
	db    *gorm.DB
	https *https.DouyinHttpService
}

func SingletonWorkorderService() *WorkorderService {
	_workorderOnce.Do(func() {
		_workorderService = &WorkorderService{
			db:    store.GOrmDB(context.Background()),
			https: https.SingletonDouyinHttpService(),
		}
	})
	return _workorderService
}

// 读取七鱼客服小程序
func (s *WorkorderService) GetQiyuMiniGameSession(ctx context.Context) (*model.AConfigMiniprogram, error) {
	mini := store.QueryDB().AConfigMiniprogram
	return mini.First()
}

// CreateSupportTicket 创建工单
func (s *WorkorderService) CreateSupportTicket(ctx context.Context, req *bean.WorkorderCreateSupportTicketReq) (string, error) {
	logger.Logger.InfofCtx(ctx, "create support ticket: %+v", req)

	// 生成工单ID (使用数字ID格式)
	orderID := util.GenerateWorkOrderID()

	// 初始化充值总额变量
	var rechargeTotalAmount int32 = req.RechargeTotalAmount
	var isFromDatabase bool = false // 标记数据是否来自数据库查询

	// 只有在req.UserID和req.GameID都不为空时才进行查询
	if req.UserID != "" && req.GameID != "" {
		// 根据user_id去找到a_order下用户充值的额度，条件为user_id, game_id，status = 4
		orderQuery := store.QueryDB().AOrder
		orders, err := orderQuery.WithContext(ctx).Where(
			orderQuery.UserID.Eq(req.UserID),
			// orderQuery.GameID.Eq(req.GameID),
			orderQuery.Status.Eq(int32(4)), // 发货成功(回调平台)
			orderQuery.IsDeleted.Is(false),
		).Find()

		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "查询用户充值订单失败: %v", err)
			// 查询失败时使用请求中的充值总额，不需要额外处理
		} else if len(orders) > 0 {
			// 计算总充值金额
			rechargeTotalAmount = 0 // 重置为0，使用查询到的数据
			for _, order := range orders {
				rechargeTotalAmount += order.Money
			}
			logger.Logger.InfofCtx(ctx, "计算用户充值总额: %d (分)", rechargeTotalAmount)
			isFromDatabase = true // 标记数据来自数据库
		} else {
			logger.Logger.InfofCtx(ctx, "用户无充值记录")
		}
	} else {
		logger.Logger.InfofCtx(ctx, "用户ID或游戏ID为空，使用请求中的充值总额: %d", rechargeTotalAmount)
	}

	// 预先计算充值总额
	if isFromDatabase {
		rechargeTotalAmount = rechargeTotalAmount / 100
	}

	// 截断CustomData字段，确保其长度不超过数据库限制
	if len(req.CustomData) > constants.MaxCustomDataLength {
		req.CustomData = util.TruncateString(req.CustomData, constants.MaxCustomDataLength)
		logger.Logger.InfofCtx(ctx, "截断过长的CustomData字段 (长度: %d)", len(req.CustomData))
	}

	// 设置默认来源
	source := req.Source
	if source == "" {
		source = "wechat" // 默认为微信，保持向后兼容
	}

	gameOpenID := ""
	if req.Source == constants.WorkorderSourceDouyin {
		gameOpenID = req.DouyinOpenID
	} else {
		gameOpenID = req.OpenID
	}

	// 创建工单记录
	workorder := &model.MWorkorder{
		OrderID:             orderID,
		GameID:              req.GameID,
		Source:              source, // 新增：工单来源
		UserID:              req.UserID,
		OpenID:              gameOpenID,   // token解析小游戏的openID
		MiniprogramOpenID:   req.DeviceID, // token解析小程序的openID
		Content:             req.Content,
		Priority:            req.Priority,
		Status:              1, // 待接单
		Category:            req.Category,
		RoleID:              req.RoleID,
		PlayerID:            req.PlayerID,
		PlayerName:          req.PlayerName,
		PlayerLevel:         req.PlayerLevel,
		RechargeTotalAmount: rechargeTotalAmount, // 已经根据数据来源处理过的充值总额
		Zone:                req.Zone,
		CustomData:          req.CustomData,
		SceneValue:          req.SceneValue,
		DeviceBrand:         req.DeviceBrand,
		DeviceModel:         req.DeviceModel,
		SystemVersion:       req.SystemVersion,
		WxVersion:           req.WxVersion,
		RechargeAmount:      req.RechargeAmount,
		Region:              req.Region,
		IssueAt:             req.IssueAt,
		HasRead:             true,
		CreatedAt:           time.Now().UnixMilli(),
		UpdatedAt:           time.Now().UnixMilli(),
	}

	// 事务开始
	txErr := s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 创建工单
		if err := tx.Create(workorder).Error; err != nil {
			return err
		}

		// 2. 记录工单操作
		// operation := &model.MWorkorderOperation{
		// 	OrderID:           orderID,
		// 	OperationType:     1, // 创建
		// 	OperationUserID:   req.UserID,
		// 	OperationUsername: req.UserID, // 用户ID当作用户名
		// 	OperationDetail:   "创建工单",
		// 	CreatedAt:         time.Now().UnixMilli(),
		// 	UpdatedAt:         time.Now().UnixMilli(),
		// }

		// if err := tx.Create(operation).Error; err != nil {
		// 	return err
		// }

		// 3. 保存附件信息
		if len(req.Attachments) > 0 {
			for _, attachment := range req.Attachments {
				workorderAttachment := &model.MWorkorderAttachment{
					OrderID:   orderID,
					FileURL:   attachment.FileURL,
					FileType:  int32(attachment.FileType),
					CreatedAt: time.Now().UnixMilli(),
					UpdatedAt: time.Now().UnixMilli(),
				}

				if err := tx.Create(workorderAttachment).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if txErr != nil {
		return "", txErr
	}

	return orderID, nil
}

// GetTicketLogs 获取工单操作日志
func (s *WorkorderService) GetTicketLogs(ctx context.Context, req *bean.WorkorderGetTicketLogsReq) ([]bean.WorkorderOperationLog, error) {
	logger.Logger.InfofCtx(ctx, "获取工单操作日志")

	// 使用 GORM Gen 查询工单操作记录
	q := store.QueryDB().MWorkorderOperation
	operations, err := q.WithContext(ctx).Where(q.OrderID.Eq(req.OrderID)).Order(q.CreatedAt.Desc()).Find()
	if err != nil {
		return nil, err
	}

	// 构建响应
	logs := make([]bean.WorkorderOperationLog, 0, len(operations))
	for _, op := range operations {
		logs = append(logs, bean.WorkorderOperationLog{
			OperationType:     op.OperationType,
			OperationUserID:   op.OperationUserID,
			OperationUsername: op.OperationUsername,
			OperationDetail:   op.OperationDetail,
			CreatedAt:         op.CreatedAt,
		})
	}

	return logs, nil
}

// GetSupportTickets 获取工单列表
func (s *WorkorderService) GetSupportTickets(ctx context.Context, req *bean.WorkorderGetSupportTicketsReq) (*bean.WorkorderSupportTicketsData, error) {
	logger.Logger.InfofCtx(ctx, "获取工单列表")

	// 早期返回逻辑：如果 DeviceID 为空，直接返回空结果集
	if req.DeviceID == "" {
		logger.Logger.InfofCtx(ctx, "DeviceID为空，返回空结果集")
		return &bean.WorkorderSupportTicketsData{
			Total:   0,
			Tickets: []bean.WorkorderSupportTicketsItem{},
		}, nil
	}

	// 使用 GORM Gen 构建查询
	q := store.QueryDB().MWorkorder

	// 构建查询条件
	conditions := make([]gen.Condition, 0)
	// if req.Header.UserID != "" {
	// 	conditions = append(conditions, q.UserID.Eq(req.Header.UserID))
	// }
	if req.GameID != "" {
		conditions = append(conditions, q.GameID.Eq(req.GameID))
	}
	if req.DeviceID != "" {
		conditions = append(conditions, q.MiniprogramOpenID.Eq(req.DeviceID))
	}
	if req.Status > 0 {
		conditions = append(conditions, q.Status.Eq(req.Status))
	}
	conditions = append(conditions, q.IsDeleted.Is(false))

	// 计算总数
	total, err := q.WithContext(ctx).Where(conditions...).Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	workorders, err := q.WithContext(ctx).
		Where(conditions...).
		Order(q.CreatedAt.Desc()).
		Offset(offset).
		Limit(req.Limit).
		Find()
	if err != nil {
		return nil, err
	}

	// 构建响应
	tickets := make([]bean.WorkorderSupportTicketsItem, 0, len(workorders))
	for _, w := range workorders {
		tickets = append(tickets, bean.WorkorderSupportTicketsItem{
			ID:                  w.ID,
			OrderID:             w.OrderID,
			Content:             w.Content,
			Status:              w.Status,
			Priority:            w.Priority,
			Category:            w.Category,
			RoleID:              w.RoleID,
			PlayerID:            w.PlayerID,
			PlayerName:          w.PlayerName,
			PlayerLevel:         w.PlayerLevel,
			RechargeTotalAmount: w.RechargeTotalAmount,
			Zone:                w.Zone,
			SceneValue:          w.SceneValue,
			DeviceBrand:         w.DeviceBrand,
			DeviceModel:         w.DeviceModel,
			SystemVersion:       w.SystemVersion,
			WxVersion:           w.WxVersion,
			RechargeAmount:      w.RechargeAmount,
			Region:              w.Region,
			AcceptUserID:        w.AcceptUserID,
			AcceptUsername:      w.AcceptUsername,
			AcceptTime:          w.AcceptTime,
			HasNewReply:         w.HasNewReply,
			LastReplyTime:       w.LastReplyTime,
			IssueAt:             w.IssueAt,
			CreatedAt:           w.CreatedAt,
		})
	}

	return &bean.WorkorderSupportTicketsData{
		Total:   total,
		Tickets: tickets,
	}, nil
}

// GetTicketDetail 获取工单详情
func (s *WorkorderService) GetTicketDetail(ctx context.Context, req *bean.WorkorderGetTicketDetailReq) (*bean.WorkorderTicketDetailData, error) {
	logger.Logger.InfofCtx(ctx, "获取工单详情")

	// 使用 store.QueryDB() 查询工单
	query := store.QueryDB().MWorkorder
	workorder, err := query.WithContext(ctx).Where(query.OrderID.Eq(req.OrderID), query.IsDeleted.Is(false)).First()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("工单不存在")
		}
		return nil, err
	}

	// 获取游戏名称
	var gameName string
	configService := SingletonConfigService() // 获取 ConfigService 实例
	if workorder.GameID != "" {               // 仅当 GameID 不为空时查询
		gameConfig, gameConfigErr := configService.GetGameConfig(ctx, workorder.GameID)
		if gameConfigErr != nil && !errors.Is(gameConfigErr, constants.ErrGameIDNotExist) {
			logger.Logger.WarnfCtx(ctx, "获取工单详情时查询游戏名称失败, GameID: %s, Error: %v", workorder.GameID, gameConfigErr)
		} else if gameConfig != nil {
			gameName = gameConfig.GameName
		}
	}

	// 构建响应
	detailData := &bean.WorkorderTicketDetailData{
		ID:                  workorder.ID,
		OrderID:             workorder.OrderID,
		GameID:              workorder.GameID,
		GameName:            gameName,
		UserID:              workorder.UserID,
		OpenID:              workorder.OpenID,
		Content:             workorder.Content,
		Priority:            workorder.Priority,
		Status:              workorder.Status,
		Category:            workorder.Category,
		AcceptUserID:        workorder.AcceptUserID,
		AcceptUsername:      workorder.AcceptUsername,
		AcceptTime:          workorder.AcceptTime,
		CompleteUserID:      workorder.CompleteUserID,
		CompleteUsername:    workorder.CompleteUsername,
		CompleteTime:        workorder.CompleteTime,
		DeviceBrand:         workorder.DeviceBrand,
		DeviceModel:         workorder.DeviceModel,
		SystemVersion:       workorder.SystemVersion,
		WxVersion:           workorder.WxVersion,
		RechargeAmount:      workorder.RechargeAmount,
		Region:              workorder.Region,
		RoleID:              workorder.RoleID,
		PlayerID:            workorder.PlayerID,
		PlayerName:          workorder.PlayerName,
		PlayerLevel:         workorder.PlayerLevel,
		RechargeTotalAmount: workorder.RechargeTotalAmount,
		Zone:                workorder.Zone,
		SceneValue:          workorder.SceneValue,
		CustomData:          workorder.CustomData,
		HasNewReply:         workorder.HasNewReply,
		LastReplyUserType:   workorder.LastReplyUserType,
		LastReplyTime:       workorder.LastReplyTime,
		IssueAt:             workorder.IssueAt,
		CreatedAt:           workorder.CreatedAt,
		UpdatedAt:           workorder.UpdatedAt,
	}

	// 查询工单附件
	attachmentQuery := store.QueryDB().MWorkorderAttachment
	attachments, err := attachmentQuery.WithContext(ctx).Where(attachmentQuery.OrderID.Eq(req.OrderID), attachmentQuery.IsDeleted.Is(false)).Find()
	if err != nil {
		return nil, err
	}

	// 构建附件列表
	detailData.Attachments = make([]bean.WorkorderAttachment, 0, len(attachments))
	for _, attachment := range attachments {
		detailData.Attachments = append(detailData.Attachments, bean.WorkorderAttachment{
			FileURL:  attachment.FileURL,
			FileType: attachment.FileType,
		})
	}

	// 查询工单回复
	replyQuery := store.QueryDB().MWorkorderReply
	replies, err := replyQuery.WithContext(ctx).Where(replyQuery.OrderID.Eq(req.OrderID), replyQuery.IsDeleted.Is(false)).Order(replyQuery.CreatedAt.Desc()).Find()
	if err != nil {
		return nil, err
	}

	// 构建回复列表
	detailData.Replies = make([]bean.WorkorderReply, 0, len(replies))
	for _, reply := range replies {
		// 查询回复附件
		replyAttachmentQuery := store.QueryDB().MWorkorderReplyAttachment
		replyAttachments, err := replyAttachmentQuery.WithContext(ctx).Where(replyAttachmentQuery.ReplyID.Eq(reply.ID), replyAttachmentQuery.IsDeleted.Is(false)).Find()
		if err != nil {
			return nil, err
		}

		// 构建回复附件列表
		attachments := make([]bean.WorkorderReplyAttachment, 0, len(replyAttachments))
		for _, attachment := range replyAttachments {
			attachments = append(attachments, bean.WorkorderReplyAttachment{
				FileURL:  attachment.FileURL,
				FileType: attachment.FileType,
			})
		}

		detailData.Replies = append(detailData.Replies, bean.WorkorderReply{
			ID:          reply.ID,
			UserID:      reply.UserID,
			Username:    reply.Username,
			Content:     reply.Content,
			UserType:    reply.UserType,
			CreatedAt:   reply.CreatedAt,
			Attachments: attachments,
		})
	}

	// 更新工单的has_new_reply字段为false（表示已读）
	if workorder.HasNewReply {
		logger.Logger.InfofCtx(ctx, "更新工单has_new_reply状态为已读, OrderID: %s", req.OrderID)
		updateQuery := store.QueryDB().MWorkorder
		_, err = updateQuery.WithContext(ctx).Where(updateQuery.ID.Eq(workorder.ID)).
			UpdateSimple(updateQuery.HasNewReply.Value(false), updateQuery.UpdatedAt.Value(time.Now().UnixMilli()))
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "更新工单has_new_reply状态失败: %v", err)
			// 非致命错误，不影响返回结果
		}
	}

	return detailData, nil
}

// ReplyTicket 回复工单
func (s *WorkorderService) ReplyTicket(ctx context.Context, req *bean.WorkorderReplyTicketReq) error {
	logger.Logger.InfofCtx(ctx, "回复工单")

	// 使用 GORM Gen 查询工单是否存在
	q := store.QueryDB().MWorkorder
	workorder, err := q.WithContext(ctx).Where(q.OrderID.Eq(req.OrderID), q.IsDeleted.Is(false)).First()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("工单不存在")
		}
		return err
	}

	// 事务开始
	err = s.db.Transaction(func(tx *gorm.DB) error {
		now := time.Now().UnixMilli()

		// 1. 创建回复记录
		reply := &model.MWorkorderReply{
			OrderID:   req.OrderID,
			UserID:    req.UserID,
			Username:  req.UserID, // 使用用户ID作为用户名
			Content:   req.Content,
			UserType:  1, // 固定为1-用户
			CreatedAt: now,
			UpdatedAt: now,
		}

		if err := tx.Create(reply).Error; err != nil {
			return err
		}

		// 2. 记录工单操作
		// operation := &model.MWorkorderOperation{
		// 	OrderID:           req.OrderID,
		// 	OperationType:     8, // 回复
		// 	OperationUserID:   req.UserID,
		// 	OperationUsername: req.UserID, // 用户ID当作用户名
		// 	OperationDetail:   "回复工单: " + req.Content,
		// 	CreatedAt:         now,
		// 	UpdatedAt:         now,
		// }

		// if err := tx.Create(operation).Error; err != nil {
		// 	return err
		// }

		// 3. 更新工单状态
		if err := tx.Model(&model.MWorkorder{}).Where("id = ?", workorder.ID).Updates(map[string]interface{}{
			"status":               workorder.Status, // 已回复
			"has_new_reply":        0,                // 新增：标记有新回复 自己回复无需标记
			"has_read":             0,                // 新增：标记未读
			"last_reply_user_type": 1,                // 回复人类型
			"updated_at":           now,
		}).Error; err != nil {
			return err
		}

		// 4. 如果有附件，保存附件信息
		if len(req.Attachments) > 0 {
			for _, attachment := range req.Attachments {
				replyAttachment := &model.MWorkorderReplyAttachment{
					ReplyID:   reply.ID,
					OrderID:   req.OrderID,
					FileURL:   attachment.FileURL,
					FileType:  attachment.FileType,
					CreatedAt: now,
					UpdatedAt: now,
				}

				if err := tx.Create(replyAttachment).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	return err
}

// AuthLogin 工单系统登录（简化版本，移除来源标识）
func (s *WorkorderService) AuthLogin(ctx context.Context, user_id, openID, miniprogramOpenID, gameID, gameName, unionID string) (*bean.QiyuAuthLoginResp, error) {
	logger.Logger.InfofCtx(ctx, "生成工单系统登录令牌, UserID: %s, GameID: %s", user_id, gameID)
	token, err := util.GenerateWorkorderToken(user_id, miniprogramOpenID, unionID, gameID, openID, gameName) // 用union_id覆盖appID
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "生成工单系统登录令牌失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "工单系统登录令牌生成成功")
	return &bean.QiyuAuthLoginResp{Token: token}, nil
}

// GetWelcomeContent 获取客服中心欢迎语
func (s *WorkorderService) GetWelcomeContent(ctx context.Context, gameID string) (*bean.WorkorderWelcomeResp, error) {
	logger.Logger.InfofCtx(ctx, "获取客服中心欢迎语, GameID: %s", gameID)

	// 直接使用配置中的QiyuConf
	appKey := config.GlobConfig.Qiyu.AppKey
	appSecret := config.GlobConfig.Qiyu.AppSecret
	returnEnv := config.GlobConfig.Qiyu.ReturnEnv

	logger.Logger.InfofCtx(ctx, "使用配置中的七鱼配置，AppKey: %s, ReturnEnv: %s", appKey, returnEnv)

	// 生成随机用户ID，用于获取欢迎语
	randomUserID := fmt.Sprintf("user_%d", time.Now().UnixNano())

	// 调用七鱼接口获取欢迎语 (仍然保留这个调用，但不使用其返回的欢迎语)
	qiyuService := SingletonQiyuService()

	// 使用七鱼机器人获取回复
	logger.Logger.InfofCtx(ctx, "调用七鱼机器人获取回复")
	replyContent, err := qiyuService.GetRobotReply(ctx, appKey, appSecret, "*", randomUserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "调用七鱼机器人获取回复失败: %v", err)
		return nil, fmt.Errorf("调用七鱼机器人获取回复失败: %w", err)
	}

	// 从m_question_welcome_message表中查询欢迎语
	logger.Logger.InfofCtx(ctx, "从m_question_welcome_message表中查询欢迎语, GameID: %s", gameID)
	welcomeQuery := store.QueryDB().MQuestionWelcomeMessage
	welcomeMessages, err := welcomeQuery.WithContext(ctx).
		Where(
			welcomeQuery.GameID.Eq(gameID),
			welcomeQuery.IsDeleted.Is(false),
		).
		Order(welcomeQuery.Weight).
		Find()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "查询欢迎语失败: %v", err)
		// 查询失败时，使用七鱼返回的欢迎语作为备选
		return &bean.WorkorderWelcomeResp{
			WelcomeContent: []string{},
			Content:        replyContent,
			CreatedAt:      time.Now().UnixMilli(),
		}, nil
	}

	// 构建欢迎语内容切片
	welcomeContents := make([]string, 0, len(welcomeMessages))
	for _, msg := range welcomeMessages {
		welcomeContents = append(welcomeContents, msg.Content)
	}

	// 如果没有查询到欢迎语，使用七鱼返回的欢迎语作为备选
	if len(welcomeContents) == 0 {
		logger.Logger.InfofCtx(ctx, "未查询到欢迎语配置，使用七鱼返回的欢迎语作为备选")
		welcomeContents = append(welcomeContents, "")
	}

	// 输出欢迎语和机器人回复内容
	logger.Logger.InfofCtx(ctx, "[欢迎语] game: %s, 欢迎语数量: %d, 机器人回复: %s", gameID, len(welcomeContents), replyContent)

	// 返回数据，包含当前时间戳
	return &bean.WorkorderWelcomeResp{
		WelcomeContent: welcomeContents,        // 欢迎语列表
		Content:        replyContent,           // 常见问题内容
		CreatedAt:      time.Now().UnixMilli(), // 添加当前时间戳
	}, nil
}

// GetBusySwitch 获取繁忙提示开关状态
func (s *WorkorderService) GetBusySwitch(ctx context.Context, gameID string) (*bean.WorkorderBusySwitchResp, error) {
	logger.Logger.InfofCtx(ctx, "获取繁忙提示开关状态, GameID: %s", gameID)

	// 查询游戏信息
	gameQuery := store.QueryDB().MGame
	game, err := gameQuery.WithContext(ctx).Where(gameQuery.GameID.Eq(gameID), gameQuery.IsDeleted.Is(false)).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "查询游戏信息失败: %v", err)
		return nil, err
	}

	// 查询忙碌开关状态
	busySwitch := false
	// 直接使用原生 SQL 查询
	var result struct {
		BusySwitch bool
	}
	err = s.db.WithContext(ctx).Table("m_workorder_busy_switch").
		Select("busy_switch").
		Where("game_id = ? AND is_deleted = ?", game.GameID, false).
		First(&result).Error
	if err == nil {
		busySwitch = result.BusySwitch
	} else if err != gorm.ErrRecordNotFound {
		logger.Logger.WarnfCtx(ctx, "查询忙碌开关状态失败: %v", err)
		// 继续执行，不返回错误
	}

	return &bean.WorkorderBusySwitchResp{
		BusySwitch: busySwitch,
	}, nil
}

// GetBusySwitchGlobal 获取全局繁忙提示开关状态
func (s *WorkorderService) GetBusySwitchGlobal(ctx context.Context) (*bean.WorkorderBusySwitchResp, error) {
	logger.Logger.InfofCtx(ctx, "获取全局繁忙提示开关状态")

	// 查询全局忙碌开关状态
	busySwitch := false

	// 使用GORM查询，不使用is_global字段（该字段不存在）
	busySwitchQuery := store.QueryDB().MWorkorderBusySwitch
	result, err := busySwitchQuery.WithContext(ctx).
		Where(busySwitchQuery.IsDeleted.Is(false)).
		// 只检索未删除的记录
		Select(busySwitchQuery.BusySwitch).
		First()

	if err == nil {
		busySwitch = result.BusySwitch
	} else if err != gorm.ErrRecordNotFound {
		logger.Logger.WarnfCtx(ctx, "查询全局忙碌开关状态失败: %v", err)
		// 继续执行，不返回错误
	}

	return &bean.WorkorderBusySwitchResp{
		BusySwitch: busySwitch,
	}, nil
}

// GetOngoingWorkorderStatus 获取当前用户是否有正在进行中的工单状态
func (s *WorkorderService) GetOngoingWorkorderStatus(ctx context.Context, req *bean.GetOngoingWorkorderStatusReq) (*bean.GetOngoingWorkorderStatusResp, error) {
	logger.Logger.InfofCtx(ctx, "开始获取用户进行中工单状态, UserID: %s, DeviceID: %s", req.UserID, req.DeviceID)

	q := store.QueryDB().MWorkorder
	conditions := []gen.Condition{
		q.Status.In(1, 2), // 状态: 1-待接单, 2-受理中
		q.IsDeleted.Is(false),
	}

	// 优先使用 UserID 查询，如果 UserID 为空，则使用 DeviceID (MiniprogramOpenID)
	if req.GameID != "" {
		conditions = append(conditions, q.GameID.Eq(req.GameID))
	}
	if req.DeviceID != "" { // Fix: header.DeviceID -> req.DeviceID
		conditions = append(conditions, q.MiniprogramOpenID.Eq(req.DeviceID)) // Fix: header.DeviceID -> req.DeviceID
	} else {
		// 如果两者都为空，无法确定用户，返回空状态
		logger.Logger.WarnfCtx(ctx, "无法确定用户身份 (UserID 和 DeviceID 都为空)")
		return &bean.GetOngoingWorkorderStatusResp{HasOngoing: false}, nil
	}

	// 查询最新的一个进行中工单
	latestOngoingWorkorder, err := q.WithContext(ctx).
		Where(conditions...).
		Order(q.CreatedAt.Desc()).
		First()

	if err != nil {
		// 如果没有找到记录，说明没有进行中的工单
		if err == gorm.ErrRecordNotFound {
			logger.Logger.InfofCtx(ctx, "用户没有进行中的工单, UserID: %s, DeviceID: %s", req.UserID, req.DeviceID) // Fix: header.UserID -> req.UserID, header.DeviceID -> req.DeviceID
			return &bean.GetOngoingWorkorderStatusResp{HasOngoing: false}, nil
		}
		// 其他数据库错误
		logger.Logger.ErrorfCtx(ctx, "查询进行中工单失败: %v", err)
		return nil, err
	}

	// 找到了进行中的工单
	logger.Logger.InfofCtx(ctx, "用户存在进行中的工单, OrderID: %s", latestOngoingWorkorder.OrderID)
	resp := &bean.GetOngoingWorkorderStatusResp{
		HasOngoing:  true,
		Content:     util.TruncateString(latestOngoingWorkorder.Content, 50), // 截断内容以作摘要
		HasNewReply: latestOngoingWorkorder.HasNewReply,
		OrderID:     latestOngoingWorkorder.OrderID,
	}

	return resp, nil
}

// GetGameList 获取客服中心游戏列表
func (s *WorkorderService) GetGameList(ctx context.Context) (*bean.WorkorderGameListResp, error) {
	logger.Logger.InfofCtx(ctx, "获取客服中心游戏列表")

	// 查询工单系统游戏展示配置
	configQuery := store.QueryDB().MWorkorderConfig
	configs, err := configQuery.WithContext(ctx).Where(configQuery.IsDeleted.Is(false)).Order(configQuery.Weight.Desc()).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "查询工单系统游戏展示配置失败: %v", err)
		return nil, err
	}

	// 收集所有游戏ID
	gameIDs := make([]string, 0, len(configs))
	for _, config := range configs {
		gameIDs = append(gameIDs, config.GameID)
	}

	// 查询游戏信息
	gameQuery := store.QueryDB().MGame
	games, err := gameQuery.WithContext(ctx).Where(gameQuery.GameID.In(gameIDs...), gameQuery.IsDeleted.Is(false)).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "查询游戏信息失败: %v", err)
		return nil, err
	}

	// 构建游戏信息映射
	gameMap := make(map[string]*model.MGame, len(games))
	for _, game := range games {
		gameMap[game.GameID] = game
	}

	// 查询工单审核开关信息
	reviewSwitchQuery := store.QueryDB().MWorkorderReviewSwitch
	reviewSwitch, err := reviewSwitchQuery.WithContext(ctx).Where(reviewSwitchQuery.IsDeleted.Is(false)).Order(reviewSwitchQuery.UpdatedAt.Desc()).First()
	if err != nil && err != gorm.ErrRecordNotFound {
		logger.Logger.WarnfCtx(ctx, "查询工单审核开关信息失败: %v", err)
		// 继续执行，不返回错误
	}

	// 默认值
	version := ""
	hasReviewSwitch := false

	// 如果找到了审核开关记录，使用其值
	if reviewSwitch != nil {
		version = reviewSwitch.Version
		hasReviewSwitch = reviewSwitch.ReviewSwitch
		logger.Logger.InfofCtx(ctx, "获取到工单审核开关信息: version=%s, review_switch=%v", version, hasReviewSwitch)
	} else {
		logger.Logger.InfofCtx(ctx, "未找到工单审核开关信息，使用默认值: version=\"\", review_switch=false")
	}

	// 构建响应
	gameItems := make([]bean.WorkorderGameItem, 0, len(configs))
	for _, config := range configs {
		game, exists := gameMap[config.GameID]
		if !exists {
			logger.Logger.WarnfCtx(ctx, "游戏信息不存在, GameID: %s", config.GameID)
			continue
		}

		gameItems = append(gameItems, bean.WorkorderGameItem{
			GameID:   game.GameID,
			GameName: game.Name,
			GameIcon: game.Icon,
			Weight:   config.Weight,
		})
	}

	return &bean.WorkorderGameListResp{
		List:            gameItems,
		Version:         version,
		HasReviewSwitch: hasReviewSwitch,
	}, nil
}
