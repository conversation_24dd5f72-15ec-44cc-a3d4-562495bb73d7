package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/go-resty/resty/v2"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

var (
	_douyinOnce    sync.Once
	_douyinService *DouyinService
)

type DouyinService struct {
	// client *resty.Client
}

func SingletonDouyinService() *DouyinService {
	_douyinOnce.Do(func() {
		_douyinService = &DouyinService{
			// client: resty.New(),
		}
	})
	return _douyinService
}

const (
	tokenURL   = "/mgplatform/api/apps/v2/token"
	loginURL   = "/mgplatform/api/apps/jscode2session"
	gamePayURL = "/api/apps/game/wallet/game_pay"
	balanceURL = "/api/apps/game/wallet/get_balance"
)

// GetAccessToken get douyin access token
func (s *DouyinService) GetAccessToken(ctx context.Context, appID, appSecret string) (*bean.DouyinAccessToken, error) {
	resp, err := resty.
		New().
		SetTimeout(5 * time.Second).
		R().
		SetContext(ctx).
		SetBody(map[string]string{
			"appid":      appID,
			"secret":     appSecret,
			"grant_type": "client_credential",
		}).Post(config.GlobConfig.Douyin.BaseURL + tokenURL)
	if err != nil {
		return nil, err
	}

	douyinAccessToken := &bean.DouyinAccessToken{}
	if err := json.Unmarshal(resp.Body(), douyinAccessToken); err != nil {
		return nil, err
	}
	return douyinAccessToken, nil
}

// UpdateDouyinConfigToken update douyin config token
func (s *DouyinService) UpdateDouyinConfigToken(ctx context.Context, gameID, accessToken string, expiresIn int) error {
	douyin := store.QueryDB().AConfigDouyin
	updates, err := douyin.WithContext(ctx).Where(douyin.GameID.Eq(gameID)).Updates(map[string]interface{}{
		"access_token":       accessToken,
		"expires_in":         expiresIn,
		"token_refreshed_at": time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}
	if updates.RowsAffected == 0 {
		return fmt.Errorf("UpdateDouyinConfigToken error updated 0 rows, gameID:%s", gameID)
	}
	return nil
}

func (s *DouyinService) LoginDouyin(ctx context.Context, code string, p *bean.DouyinParam) (*bean.DouyinResp, error) {
	resp, err := resty.
		New().
		SetTimeout(5 * time.Second).
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appid":  p.AppID,
			"secret": p.AppSecret,
			"code":   code,
		}).
		Get(config.GlobConfig.Douyin.BaseURL + loginURL)
	if err != nil {
		return nil, err
	}

	douyinRes := &bean.DouyinResp{}
	if err := json.Unmarshal(resp.Body(), &douyinRes); err != nil {
		return nil, err
	}

	if douyinRes.ErrCode != 0 {
		logger.Logger.Errorf("Service LoginDouyin: 成功调用，但获取api结果失败，错误信息: %v", douyinRes)
		return nil, fmt.Errorf("success call, but get api error: %v", douyinRes)
	}

	return douyinRes, nil
}

func (s *DouyinService) GetDouyinConfs(ctx context.Context) ([]*model.AConfigDouyin, error) {
	douyin := store.QueryDB().AConfigDouyin
	douyinCtx := douyin.WithContext(ctx)
	douyinList, err := douyinCtx.Where(douyin.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	return douyinList, nil
}

func (s *DouyinService) GetDouyinConf(ctx context.Context, gameID string) (*model.AConfigDouyin, error) {
	douyin := store.QueryDB().AConfigDouyin
	douyinCtx := douyin.WithContext(ctx)
	if gameID != "" {
		douyinCtx = douyinCtx.Where(douyin.GameID.Eq(gameID))
	}
	douyinInfo, err := douyinCtx.Where(douyin.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("GetDouyinConfig 未找到对应游戏的抖音配置信息, gameID: %s", gameID)
	} else if err != nil {
		return nil, err
	}
	return douyinInfo, nil
}

// GetUserInfo 获取用户信息
func (s *DouyinService) GetUserInfo(ctx context.Context, gameID string, code string, p *bean.DouyinUser, channel *bean.ChannelInfo) (*bean.User, error) {
	// 分布式锁
	openIDLockKey := fmt.Sprintf("%s:%s", constants.SystemLoginDouyinLockKey, code)
	isLock := redis.Lock(ctx, openIDLockKey, constants.SystemLoginDouyinLockExpire*time.Second)
	if !isLock {
		logger.Logger.Warnf("GetUserInfoByOpenID server is busy, game_id :%s, openID :%s", gameID, p.OpenID)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, openIDLockKey)

	douyinUser := store.QueryDB().AUserDouyin
	userInfo, err := douyinUser.WithContext(ctx).Where(douyinUser.OpenID.Eq(p.OpenID), douyinUser.IsDeleted.Zero()).First()
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		// 未找到记录，创建新用户
		return s.createDouyinUser(ctx, gameID, p, channel)
	}

	// 更新SessionKey
	if err := updateSessionKey(ctx, userInfo.ID, p.SessionKey); err != nil {
		return nil, err
	}

	userRes, err := mapUserInfoToUser(userInfo)
	if err != nil {
		return nil, err
	}
	userRes.IsRegister = false
	userRes.ChannelInfo = channel
	return userRes, nil
}

// 新建Douyin用户和AUser
func (s *DouyinService) createDouyinUser(ctx context.Context, gameID string, p *bean.DouyinUser, channel *bean.ChannelInfo) (*bean.User, error) {
	douyinUser := store.QueryDB().AUserDouyin
	user := store.QueryDB().AUser

	newUserID := util.UUID()
	douyinUserModel := &model.AUserDouyin{
		UserID:     newUserID,
		OpenID:     p.OpenID,
		UnionID:    p.UnionID,
		SessionKey: p.SessionKey,
	}
	userModel := &model.AUser{
		GameID:  gameID,
		UserID:  newUserID,
		Channel: channel.Channel,
		AdFrom:  channel.ADFrom,
	}
	if err := douyinUser.WithContext(ctx).Create(douyinUserModel); err != nil {
		return nil, err
	}
	if err := user.WithContext(ctx).Create(userModel); err != nil {
		return nil, err
	}
	return &bean.User{
		IsRegister: true,
		UserInfo: &bean.UserInfo{
			UserID:     newUserID,
			RegisterAt: time.Now().UnixMilli(),
		},
		ChannelInfo: &bean.ChannelInfo{
			Channel: channel.Channel,
			ADFrom:  channel.ADFrom,
			OpenID:  channel.OpenID,
		},
	}, nil
}

// 更新SessionKey
func updateSessionKey(ctx context.Context, id int32, sessionKey string) error {
	douyinUser := store.QueryDB().AUserDouyin
	if _, err := douyinUser.WithContext(ctx).Where(douyinUser.ID.Eq(id)).
		UpdateSimple(douyinUser.SessionKey.Value(sessionKey)); err != nil {
		return err
	}
	return nil
}

// mapUserInfoToUser 映射用户信息到UserBean
func mapUserInfoToUser(userInfo *model.AUserDouyin) (*bean.User, error) {
	userRes := &bean.User{}
	err := copier.Copy(&userRes, userInfo)
	if err != nil { // Log error or handle it
		return nil, err
	}

	// 设置注册时间
	if userRes.UserInfo != nil {
		userRes.UserInfo.RegisterAt = userInfo.CreatedAt
	}

	return userRes, nil
}

// GetGamePayParam 获取游戏支付参数
func (s *DouyinService) GetGamePayParam(ctx context.Context, orderID string, buyQuantity int32) *bean.DouyinOrder {
	// 确保 customId 不为空，这是抖音 API 的必填字段
	if orderID == "" {
		// 如果 orderID 为空，说明前面的业务逻辑出现了严重错误
		logger.Logger.ErrorfCtx(ctx, "[GetGamePayParam] orderID为空，业务逻辑验证错误，无法生成支付参数")
		return nil
	}

	// 确保 buyQuantity 大于 0，这是抖音 API 的必要条件
	if buyQuantity <= 0 {
		logger.Logger.ErrorfCtx(ctx, "[GetGamePayParam] buyQuantity无效，order_id: %s, buy_quantity: %d，必须大于0", orderID, buyQuantity)
		return nil
	}

	// 构建 extraInfo，包含订单相关信息用于回调识别
	extraInfo := fmt.Sprintf(`{"order_id":"%s","buy_quantity":%d,"timestamp":%d}`,
		orderID, buyQuantity, time.Now().Unix())

	logger.Logger.InfofCtx(ctx, "[GetGamePayParam] 构建抖音支付参数, order_id: %s, buy_quantity: %d", orderID, buyQuantity)

	return &bean.DouyinOrder{
		Mode:         "game",
		Env:          0,
		CurrencyType: "CNY",
		Platform:     "android",
		BuyQuantity:  buyQuantity,
		ZoneId:       "1",
		CustomId:     orderID,
		ExtraInfo:    extraInfo,
	}
}

// GetSmallDiamondPayParam 获取小额钻石支付参数
func (s *DouyinService) GetSmallDiamondPayParam(ctx context.Context, orderID string, orderAmount int32) *bean.DouyinOrder {
	// 确保 customId 不为空，这是抖音 API 的必填字段
	if orderID == "" {
		logger.Logger.ErrorfCtx(ctx, "[GetSmallDiamondPayParam] orderID为空，业务逻辑验证错误，无法生成支付参数")
		return nil
	}

	// 确保 orderAmount 大于 0
	if orderAmount <= 0 {
		logger.Logger.ErrorfCtx(ctx, "[GetSmallDiamondPayParam] orderAmount无效，order_id: %s, order_amount: %d，必须大于0", orderID, orderAmount)
		return nil
	}

	// 构建 extraInfo，包含订单相关信息用于回调识别
	extraInfo := fmt.Sprintf(`{"order_id":"%s","order_amount":%d,"timestamp":%d,"payment_type":"small_diamond"}`,
		orderID, orderAmount, time.Now().Unix())

	logger.Logger.InfofCtx(ctx, "[GetSmallDiamondPayParam] 构建小额钻石支付参数, order_id: %s, order_amount: %d", orderID, orderAmount)

	return &bean.DouyinOrder{
		Mode:         "game",
		Env:          0,
		CurrencyType: "CNY",
		Platform:     "android",
		// BuyQuantity 不传此参数（小额钻石支付特性）
		ZoneId:      "1",
		CustomId:    orderID,
		ExtraInfo:   extraInfo,
		GoodType:    2,           // 小额钻石支付固定为2
		OrderAmount: orderAmount, // 订单金额（单位：分）
	}
}

// GetIOSGamePayParam 获取iOS游戏支付参数（客服场景专用）
func (s *DouyinService) GetIOSGamePayParam(ctx context.Context, orderID string, buyQuantity int32) *bean.DouyinOrder {
	// 确保 customId 不为空，这是抖音 API 的必填字段
	if orderID == "" {
		logger.Logger.ErrorfCtx(ctx, "[GetIOSGamePayParam] orderID为空，业务逻辑验证错误，无法生成支付参数")
		return nil
	}

	// 确保 buyQuantity 大于 0，这是抖音 API 的必要条件
	if buyQuantity <= 0 {
		logger.Logger.ErrorfCtx(ctx, "[GetIOSGamePayParam] buyQuantity无效，order_id: %s, buy_quantity: %d，必须大于0", orderID, buyQuantity)
		return nil
	}

	// 构建 extraInfo，包含订单相关信息用于回调识别
	extraInfo := fmt.Sprintf(`{"order_id":"%s","buy_quantity":%d,"timestamp":%d,"platform":"ios"}`,
		orderID, buyQuantity, time.Now().Unix())

	logger.Logger.InfofCtx(ctx, "[GetIOSGamePayParam] 构建iOS抖音支付参数, order_id: %s, buy_quantity: %d", orderID, buyQuantity)

	return &bean.DouyinOrder{
		Mode:         "game",
		Env:          0,
		CurrencyType: "CNY",
		Platform:     "ios", // iOS 平台
		BuyQuantity:  buyQuantity,
		ZoneId:       "1",
		CustomId:     orderID,
		ExtraInfo:    extraInfo,
	}
}

// GetIOSSmallDiamondPayParam 获取iOS小额钻石支付参数（客服场景专用）
func (s *DouyinService) GetIOSSmallDiamondPayParam(ctx context.Context, orderID string, orderAmount int32) *bean.DouyinOrder {
	// 确保 customId 不为空，这是抖音 API 的必填字段
	if orderID == "" {
		logger.Logger.ErrorfCtx(ctx, "[GetIOSSmallDiamondPayParam] orderID为空，业务逻辑验证错误，无法生成支付参数")
		return nil
	}

	// 确保 orderAmount 大于 0
	if orderAmount <= 0 {
		logger.Logger.ErrorfCtx(ctx, "[GetIOSSmallDiamondPayParam] orderAmount无效，order_id: %s, order_amount: %d，必须大于0", orderID, orderAmount)
		return nil
	}

	// 构建 extraInfo，包含订单相关信息用于回调识别
	extraInfo := fmt.Sprintf(`{"order_id":"%s","order_amount":%d,"timestamp":%d,"payment_type":"small_diamond","platform":"ios"}`,
		orderID, orderAmount, time.Now().Unix())

	logger.Logger.InfofCtx(ctx, "[GetIOSSmallDiamondPayParam] 构建iOS小额钻石支付参数, order_id: %s, order_amount: %d", orderID, orderAmount)

	return &bean.DouyinOrder{
		Mode:         "game",
		Env:          0,
		CurrencyType: "CNY",
		Platform:     "ios", // iOS 平台
		// BuyQuantity 不传此参数（小额钻石支付特性）
		ZoneId:      "1",
		CustomId:    orderID,
		ExtraInfo:   extraInfo,
		GoodType:    2,           // 小额钻石支付固定为2
		OrderAmount: orderAmount, // 订单金额（单位：分）
	}
}

// VerifySign 验证签名
func (s *DouyinService) VerifySign(token string, timestamp string, nonce string, msg string, sign string) bool {
	sortedString := make([]string, 4)
	sortedString = append(sortedString, token)
	sortedString = append(sortedString, timestamp)
	sortedString = append(sortedString, nonce)
	sortedString = append(sortedString, msg)
	// 需要对这些参数按字符串自然大小进行排序
	sort.Strings(sortedString)
	// 使用SHA1算法
	h := sha1.New()
	h.Write([]byte(strings.Join(sortedString, "")))

	bs := h.Sum(nil)
	signature := fmt.Sprintf("%x", bs)

	// signature 一致表示请求来源于 字节小程序服务端
	return signature == sign
}

// CallbackSuccessUpdate 更新订单状态（带重复回调检查）
func (s *DouyinService) CallbackSuccessUpdate(ctx context.Context, openID, orderID string, amountCent int32, orderNoChannel string, callbackOriginData string) error {
	logger.Logger.InfofCtx(ctx, "[CallbackSuccessUpdate] 开始更新订单状态, order_id: %s, amount_cent: %d", orderID, amountCent)

	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)

	// 添加状态检查，只有未支付成功的订单才能更新，防止重复回调
	updated, err := orderCtx.
		Where(order.OrderID.Eq(orderID)).
		Where(order.IsDeleted.Zero()).
		Where(order.Status.Lt(constants.PaymentWechatPaySuccess)). // 只更新状态小于支付成功的订单
		UpdateSimple(
			order.Status.Value(constants.PaymentWechatPaySuccess),
			order.PayerOpenID.Value(openID),
			order.CurrencyPrice.Value(amountCent),
			order.ThirdPartyTransactionID.Value(orderNoChannel),
			order.CallbackOriginData.Value(callbackOriginData),
		)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[CallbackSuccessUpdate] 更新订单状态失败, order_id: %s, error: %v", orderID, err)
		return err
	}

	// 如果没有更新任何行，检查订单是否已经是成功状态
	if updated.RowsAffected == 0 {
		// 查询订单当前状态
		existingOrder, checkErr := orderCtx.Where(order.OrderID.Eq(orderID)).Where(order.IsDeleted.Zero()).First()
		if checkErr != nil {
			logger.Logger.ErrorfCtx(ctx, "[CallbackSuccessUpdate] 查询订单状态失败, order_id: %s, error: %v", orderID, checkErr)
			return errors.New("CallbackSuccessUpdate The order is abnormal and may not be found, order id: " + orderID)
		}

		// 如果订单已经是成功状态，说明是重复回调，不返回错误
		if existingOrder.Status >= constants.PaymentWechatPaySuccess {
			logger.Logger.InfofCtx(ctx, "[CallbackSuccessUpdate] 订单已处于成功状态，可能是重复回调, order_id: %s, current_status: %d", orderID, existingOrder.Status)
			return nil // 不返回错误，避免重试
		}

		// 订单存在但状态异常
		logger.Logger.ErrorfCtx(ctx, "[CallbackSuccessUpdate] 订单状态异常, order_id: %s, current_status: %d", orderID, existingOrder.Status)
		return errors.New("CallbackSuccessUpdate The order status is abnormal, order id: " + orderID)
	}

	logger.Logger.InfofCtx(ctx, "[CallbackSuccessUpdate] 订单状态更新成功, order_id: %s, affected_rows: %d", orderID, updated.RowsAffected)
	return nil
}

func (s *DouyinService) GetPaySign(p *bean.DeductGameCurrencyParam) string {
	pStr := fmt.Sprintf("access_token=%s&amt=%d&appid=%s&bill_no=%s&openid=%s&pf=%s&ts=%d&zone_id=%s", p.AccessToken, p.Amt, p.AppID, p.BillNo, p.OpenID, p.PF, p.Ts, p.ZoneID)
	allStr := pStr + fmt.Sprintf("&org_loc=%s&method=POST", gamePayURL)
	logger.Logger.Infof("GetPaySign allStr: %s", allStr)
	key := []byte(p.MpSig)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(allStr))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// DeductGameCurrency 扣除游戏货币
func (s *DouyinService) DeductGameCurrency(ctx context.Context, p *bean.DeductGameCurrencyParam) (*bean.DouyinWallet, error) {
	p.MpSig = s.GetPaySign(p) // 将支付密钥转换为支付Sign

	bodyByte, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	resp, err := resty.
		New().
		SetRetryCount(3).
		SetRetryWaitTime(2*time.Second).
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetHeader("content-type", "application/json").
		SetBody(bodyByte).
		Post(config.GlobConfig.Douyin.ToutiaoURL + gamePayURL)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("DeductGameCurrency StatusCode: %d", resp.StatusCode())
	}

	wallet := &bean.DouyinWallet{}
	err = json.Unmarshal(resp.Body(), wallet)
	if err != nil {
		return nil, err
	}
	if wallet.ErrCode != 0 {
		return nil, fmt.Errorf("DeductGameCurrency ErrCode: %d, ErrMsg: %s", wallet.ErrCode, wallet.ErrMsg)
	}

	return wallet, nil
}

func (s *DouyinService) GetBalanceSign(p *bean.DouyinBalanceParam) string {
	pStr := fmt.Sprintf("access_token=%s&appid=%s&openid=%s&pf=%s&ts=%d&zone_id=%s", p.AccessToken, p.AppID, p.OpenID, p.PF, p.Ts, p.ZoneID)
	allStr := pStr + fmt.Sprintf("&org_loc=%s&method=POST", balanceURL)
	logger.Logger.Infof("GetPaySign allStr: %s", allStr)
	key := []byte(p.MpSig)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(allStr))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// GetGameBalance 获取游戏币余额
func (s *DouyinService) GetGameBalance(ctx context.Context, p *bean.DouyinBalanceParam) (*bean.DouyinBalance, error) {
	p.MpSig = s.GetBalanceSign(p) // 将支付密钥转换为支付Sign

	bodyByte, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	resp, err := resty.
		New().
		// SetRetryCount(3).
		// SetRetryWaitTime(2*time.Second).
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetHeader("content-type", "application/json").
		SetBody(bodyByte).
		Post(config.GlobConfig.Douyin.ToutiaoURL + balanceURL)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("GetGameBalance StatusCode: %d", resp.StatusCode())
	}

	balance := &bean.DouyinBalance{}
	err = json.Unmarshal(resp.Body(), balance)
	if err != nil {
		return nil, err
	}
	if balance.ErrCode != 0 {
		return nil, fmt.Errorf("GetGameBalance ErrCode: %d, ErrMsg: %s", balance.ErrCode, balance.ErrMsg)
	}
	return balance, nil
}

// IsNewUser 检查用户是否为新用户（通过OpenID判断）
func (s *DouyinService) IsNewUser(ctx context.Context, openID string) (bool, error) {
	douyinUser := store.QueryDB().AUserDouyin
	count, err := douyinUser.WithContext(ctx).
		Where(douyinUser.IsDeleted.Zero()).
		Where(douyinUser.OpenID.Eq(openID)).Count()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DouyinService IsNewUser check failed: %v", err)
		return false, err
	}

	// 如果count为0，说明是新用户
	return count == 0, nil
}
