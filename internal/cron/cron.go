package cron

import (
	"context"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/robfig/cron/v3"
)

var <PERSON>rons Cron

type Cron struct {
	cron *cron.Cron
}

func InitCron() {
	c := cron.New()
	c.Start()
	Crons.cron = c

	Crons.refreshTokenByMinigame()
	Crons.refreshTokenBySubscribe()
	Crons.refreshTokenByDouyin()
	Crons.refreshTokenByQQ()
	Crons.refreshTokenByMiniprogram()
	Crons.refreshMiniprogramUrlLink()
	// Crons.callbackToProductServer() // 使用asynq完成
	Crons.workorderStatsEmailCron()
}

const (
	everyTenSeconds       = "@every 10s"
	tokenExpirationBuffer = 300 // 提前5分钟（300秒）更新
)

const (
	everyHour = "@every 1h" // 每小时
)

func (c Cron) StartCron(timer string, f func()) {
	_, err := c.cron.AddFunc(timer, f)
	if err != nil {
		logger.Logger.Errorf("start cron error %v", err)
	}
}

// shouldRefreshToken 判断是否过期
func shouldRefreshToken(accessToken string, tokenRefreshedAt int64, expiresIn int32) bool {
	if accessToken == "" {
		return true
	}

	// 如果TokenRefreshedAt为0（初始值），则立即刷新
	if tokenRefreshedAt == 0 {
		return true
	}

	// 计算过期时间，使用TokenRefreshedAt替代UpdatedAt
	expirationTime := (tokenRefreshedAt / 1000) + int64(expiresIn)
	// 提前5分钟刷新，保证平滑过渡
	return time.Now().Unix() >= (expirationTime - tokenExpirationBuffer)
}

func (c Cron) refreshTokenByMinigame() {
	ctx := context.Background()
	minigameService := service.SingletonMinigameService()

	c.StartCron(everyTenSeconds, func() {
		configs, err := minigameService.GetMinigameConfigs(ctx)
		if err != nil {
			logger.Logger.Errorf("GetMinigameConfigs err: %v", err.Error())
			return
		}

		for _, config := range configs {
			if shouldRefreshToken(config.AccessToken, config.TokenRefreshedAt, config.ExpiresIn) {

				logger.Logger.InfofCtx(ctx, "Refreshing minigame token for game_id=%s at %s, expiresIn=%d, tokenRefreshedAt=%d",
					config.GameID,
					time.Now().Format("2006-01-02 15:04:05"),
					config.ExpiresIn,
					config.TokenRefreshedAt)
				c.refreshTokenForConfig(ctx, config)
			}
		}
	})
}

func (c Cron) refreshTokenForDouyinConfig(ctx context.Context, config *model.AConfigDouyin) {
	logger.Logger.InfofCtx(ctx, "refreshTokenForDouyinConfig start, game_id: %s, time: %s, token: %s, expiresIn: %d, tokenRefreshedAt: %d", config.GameID, time.Now().Format("2006-01-02 15:04:05"), config.AccessToken, config.ExpiresIn, config.TokenRefreshedAt)

	lockKey := fmt.Sprintf("token:refresh:douyin:%s", config.GameID)
	// 使用 Redis 分布式锁
	if !redis.Lock(ctx, lockKey, 30*time.Second) {
		logger.Logger.WarnfCtx(ctx, "Token refresh already in progress in another pod for game_id: %s", config.GameID)
		return
	}
	defer redis.UnLock(ctx, lockKey)

	douyin := service.SingletonDouyinService()

	token, err := douyin.GetAccessToken(ctx, config.AppID, config.AppSecret)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "refreshTokenForDouyinConfig GetAccessToken err: %v", err)
		return
	}

	err = douyin.UpdateDouyinConfigToken(ctx, config.GameID, token.Data.AccessToken, token.Data.ExpiresIn)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "refreshTokenForDouyinConfig UpdateMinigameConfigToken err: %v", err)
		return
	}

	// c.handleGravityTokenRefresh(ctx, &model.AConfigMinigame{GameID: config.GameID}, token.Data.AccessToken) // AConfigMinigame 为了兼容微信结构
}

func (c Cron) refreshTokenForConfig(ctx context.Context, config *model.AConfigMinigame) {
	logger.Logger.InfofCtx(ctx, "refreshTokenForConfig start, game_id: %s, time: %s, token: %s, expiresIn: %d, tokenRefreshedAt: %d", config.GameID, time.Now().Format("2006-01-02 15:04:05"), config.AccessToken, config.ExpiresIn, config.TokenRefreshedAt)

	lockKey := fmt.Sprintf("token:refresh:minigame:%s", config.GameID)
	// 使用 Redis 分布式锁
	if !redis.Lock(ctx, lockKey, 30*time.Second) {
		logger.Logger.Warnf("Token refresh already in progress in another pod for game_id: %s", config.GameID)
		return
	}
	defer redis.UnLock(ctx, lockKey)

	minigameService := service.SingletonMinigameService()

	token, err := minigameService.GetAccessToken(ctx, config.AppID, config.AppSercet)
	if err != nil {
		logger.Logger.Errorf("GetAccessToken err: %v", err)
		return
	}

	err = minigameService.UpdateMinigameConfigToken(ctx, config.GameID, token.AccessToken, token.ExpiresIn)
	if err != nil {
		logger.Logger.Errorf("UpdateMinigameConfigToken err: %v", err)
		return
	}

	c.handleGravityTokenRefresh(ctx, config, token.AccessToken)
}

func (c Cron) handleGravityTokenRefresh(ctx context.Context, config *model.AConfigMinigame, token string) {
	// 打印日志，记录时间
	logger.Logger.InfofCtx(ctx, "handleGravityTokenRefresh start, game_id: %s, time: %s, token: %s", config.GameID, time.Now().Format("2006-01-02 15:04:05"), token)

	accessToken, appID, err := service.SingletonUserService().GetBindGravityAppID(ctx, config.GameID)
	if err != nil {
		logger.Logger.Errorf("handleGravityTokenRefresh GetBindGravityAppID err: %v", err)
		return
	} else if accessToken == "" || appID == "" {
		logger.Logger.Warnf("handleGravityTokenRefresh accessToken or appID is empty, game_id: %s", config.GameID)
		return
	}

	err = service.SingletonGravityEngineService().RefreshToken(ctx, appID, accessToken, token)
	if err != nil {
		logger.Logger.Errorf("handleGravityTokenRefresh RefreshToken err: %v", err)
		return
	}
}

func (c Cron) refreshTokenBySubscribe() {
	ctx := context.Background()
	subscribeService := service.SingletonSubscribeService()

	c.StartCron(everyTenSeconds, func() {
		configs, err := subscribeService.GetSubscribeConfigs(ctx)
		if err != nil {
			logger.Logger.Errorf("GetSubscribeConfigs err: %v", err.Error())
			return
		}
		for _, conf := range configs {
			if shouldRefreshToken(conf.AccessToken, conf.UpdatedAt, conf.ExpiresIn) {
				c.refreshSubscribeToken(ctx, conf)
			}
		}
	})
}

func (c Cron) refreshSubscribeToken(ctx context.Context, conf *model.AConfigSubscribe) {
	miniGameService := service.SingletonMinigameService()

	token, err := miniGameService.GetAccessToken(ctx, conf.AppID, conf.AppSercet)
	if err != nil {
		logger.Logger.Errorf("GetAccessToken err: %v", err.Error())
		return
	}

	ticket, err := miniGameService.GetJSAPITicket(ctx, token.AccessToken)
	if err != nil {
		logger.Logger.Errorf("GetJSAPITicket err: %v", err.Error())
		return
	}

	updateInfo := map[string]interface{}{
		"access_token":      token.AccessToken,
		"expires_in":        token.ExpiresIn,
		"ticket":            ticket.Ticket,
		"ticket_expires_in": ticket.ExpiresIn,
	}
	err = service.SingletonSubscribeService().UpdateSubscribeConfig(ctx, conf.ID, updateInfo)
	if err != nil {
		logger.Logger.Errorf("UpdateSubscribeConfig err: %v", err.Error())
		return
	}
}

// refreshTokenByDouyin // 刷新 douyin token
func (c Cron) refreshTokenByDouyin() {
	ctx := context.Background()
	douyinService := service.SingletonDouyinService()
	c.StartCron(everyTenSeconds, func() {
		configs, err := douyinService.GetDouyinConfs(ctx)
		if err != nil {
			logger.Logger.Errorf("GetDouyinConfigs err: %v", err.Error())
			return
		}
		for _, config := range configs {
			if shouldRefreshToken(config.AccessToken, config.TokenRefreshedAt, config.ExpiresIn) {

				logger.Logger.InfofCtx(ctx, "Refreshing Douyin token for game_id=%s at %s, expiresIn=%d, tokenRefreshedAt=%d",
					config.GameID,
					time.Now().Format("2006-01-02 15:04:05"),
					config.ExpiresIn,
					config.TokenRefreshedAt)

				c.refreshTokenForDouyinConfig(ctx, config)
			}
		}
	})
}

// refreshTokenByQQ // 刷新 QQ token
func (c Cron) refreshTokenByQQ() {
	ctx := context.Background()
	qqService := service.SingletonQQService()

	c.StartCron(everyTenSeconds, func() {
		configs, err := qqService.GetQQConfs(ctx)
		if err != nil {
			logger.Logger.Errorf("GetQQConfigs err: %v", err.Error())
			return
		}
		for _, config := range configs {
			if shouldRefreshToken(config.AccessToken, config.UpdatedAt, config.ExpiresIn) {
				err := qqService.RefreshTokenForQQConfig(ctx, config)
				if err != nil {
					logger.Logger.Errorf("RefreshTokenForQQConfig err: %v", err)
				}
			}
		}
	})
}

// refreshTokenByMiniprogram
func (c Cron) refreshTokenByMiniprogram() {
	ctx := context.Background()
	minigameService := service.SingletonMinigameService()

	c.StartCron(everyTenSeconds, func() {
		config, err := minigameService.GetMiniprogramConfig(ctx)
		if err != nil {
			logger.Logger.Errorf("GetMinigameConfigs err: %v", err.Error())
			return
		}

		if shouldRefreshToken(config.AccessToken, config.UpdatedAt, config.ExpiresIn) {
			c.refreshTokenForMiniprogramConfig(ctx, config)
		}
	})
}

func (c Cron) refreshTokenForMiniprogramConfig(ctx context.Context, config *model.AConfigMiniprogram) {
	minigameService := service.SingletonMinigameService()

	token, err := minigameService.GetAccessToken(ctx, config.AppID, config.AppSecret)
	if err != nil {
		logger.Logger.Errorf("GetAccessToken err: %v", err)
		return
	}

	err = minigameService.UpdateMiniprogramConfigToken(ctx, token.AccessToken, token.ExpiresIn)
	if err != nil {
		logger.Logger.Errorf("UpdateMiniprogramConfigToken err: %v", err)
		return
	}
}

// refreshMiniprogramUrlLink 刷新小程序 url link
func (c Cron) refreshMiniprogramUrlLink() {
	if !config.GlobConfig.Miniprogram.IsRefreshURLLink {
		logger.Logger.Warn("refreshMiniprogramUrlLink is disabled")
		return
	}

	ctx := context.Background()
	minigameService := service.SingletonMinigameService()

	c.StartCron(everyHour, func() {
		logger.Logger.InfofCtx(ctx, "refreshMiniprogramUrlLink start , now time: %s", time.Now().Format("2024-01-01 00:00:00"))

		config, err := minigameService.GetMiniprogramConfig(ctx)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "GetMiniprogramConfig err: %v", err.Error())
			return
		}
		// config.url_link_created_at 距离最新时间超过720小时，则刷新
		if time.Now().UnixMilli()-config.URLLinkCreatedAt > 719*3600*1000 { // 30天-1小时之后刷新
			_, err = minigameService.RefreshMiniprogramUrlLink(ctx, config.AccessToken)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "RefreshMiniprogramUrlLink time: %s err: %v", time.Now().Format("2024-01-01 00:00:00"), err.Error())
			}
			logger.Logger.ErrorWithFiled(map[string]interface{}{
				"app_id": config.AppID,
				"extra":  "小程序链接Link触发更新,请去七鱼后台完成替换",
			}, "小程序链接Link触发更新")
		}
	})
}

// workorderStatsEmailCron 工单统计邮件定时任务
func (c Cron) workorderStatsEmailCron() {
	cronSpec := config.GlobConfig.EmailTask.WorkorderStats.CronSpec

	// 创建独立的cron实例，支持6字段格式（秒 分 时 日 月 周）
	emailCron := cron.New(cron.WithParser(cron.NewParser(
		cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor,
	)))
	emailCron.Start()

	// 添加执行超时控制
	ctx := context.Background()

	_, err := emailCron.AddFunc(cronSpec, func() {
		// 使用分布式锁防止多实例重复执行
		if !redis.Lock(ctx, constants.RedisLockWorkorderStatsEmail, constants.LockWorkorderStatsEmailDuration) {
			logger.Logger.InfofCtx(ctx, "[executeWorkorderStatsEmail] 任务正在其他实例执行中，跳过")
			return
		}
		defer redis.UnLock(ctx, constants.RedisLockWorkorderStatsEmail)

		workorderStatsService := service.SingletonWorkorderStatsService()
		logger.Logger.InfofCtx(ctx, "[executeWorkorderStatsEmail] 开始执行工单统计邮件任务")

		startTime := time.Now()
		err := workorderStatsService.GenerateAndSendStatsMail(ctx)
		duration := time.Since(startTime)

		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[executeWorkorderStatsEmail] 工单统计邮件任务执行失败，耗时: %v, 错误: %v", duration, err)
		} else {
			logger.Logger.InfofCtx(ctx, "[executeWorkorderStatsEmail] 工单统计邮件任务执行完成，耗时: %v", duration)
		}
	})

	if err != nil {
		logger.Logger.Errorf("[workorderStatsEmailCron] 添加定时任务失败: %v", err)
	}
}
