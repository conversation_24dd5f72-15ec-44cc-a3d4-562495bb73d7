package logic

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"unicode/utf8"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/cron"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_sensitiveOnce  sync.Once
	_sensitiveLogic *SensitiveLogic
)

type SensitiveLogic struct {
	sensitiveService    *service.SensitiveService
	minigameService     *service.MinigameService
	userService         *service.UserService
	trieService         *service.TrieService
	douyinService       *service.DouyinService
	tencentCloudService *service.TencentCloudService
}

func SingletonSensitiveLogic() *SensitiveLogic {
	_sensitiveOnce.Do(func() {
		_sensitiveLogic = &SensitiveLogic{
			sensitiveService:    service.SingletonSensitiveService(),
			minigameService:     service.SingletonMinigameService(),
			userService:         service.SingletonUserService(),
			trieService:         service.SingletonTrieService(),
			douyinService:       service.SingletonDouyinService(),
			tencentCloudService: service.SingletonTencentCloudService(),
		}
	})
	return _sensitiveLogic
}

// VerifySensitiveMessage 敏感词检测
func (l *SensitiveLogic) VerifySensitiveMessage(ctx context.Context, req *bean.VerifySensitiveMessageReq) (*bean.VerifySensitiveMessageRes, error) {
	logger.Logger.Infof("SensitiveLogic VerifySensitiveMessage req: %+v", req)
	if len(req.Msg) > constants.SensitiveWordMaxLength {
		return nil, fmt.Errorf("SensitiveService VerifySensitiveMessage msg length > 2500")
	}

	resp := &bean.VerifySensitiveMessageRes{}
	var err error
	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		resp, err = l.wechatSensitiveMessage(ctx, req.GameID, req.UserID, req.Msg)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic wechatSensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic wechat FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	case constants.PlatformTypeDouyin:
		resp, err = l.douyinSensitiveMessage(ctx, req.GameID, req.Msg)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic douyinSensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic douyin FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	}

	wordMap := cron.GetSensitiveWordLevelByGameID(req.GameID)
	ignoreCase := cron.GetSensitiveWordConfigByGameID(req.GameID)

	// 清空字典树，确保每次都是干净的状态
	l.trieService.Clear()

	// 创建一个从处理后的词到原始词的映射（用于忽略大小写的情况）
	var processedToOriginalMap map[string]string
	if ignoreCase == 1 {
		processedToOriginalMap = make(map[string]string)
	}

	// 只将有效的敏感词（等级不为0）添加到字典树中
	for word, level := range wordMap {
		if level > 0 {
			// 根据 ignoreCase 配置决定添加到字典树的格式
			wordToAdd := word
			if ignoreCase == 1 {
				wordToAdd = strings.ToLower(word)
				// 建立映射关系，用于后续查找原始词
				processedToOriginalMap[wordToAdd] = word
			}
			l.trieService.AddWord(wordToAdd)
		}
	}

	// 利用反查询把词汇和等级找出
	words := l.trieService.CheckByCase(req.Msg, ignoreCase)

	var detail []*bean.SensitiveMessagePlatformDetail
	for _, word := range words {
		// 获取原始词用于查找等级
		originalWord := word
		if ignoreCase == 1 && processedToOriginalMap != nil {
			if orig, exists := processedToOriginalMap[strings.ToLower(word)]; exists {
				originalWord = orig
			}
		}

		level := wordMap[originalWord]
		// 由于字典树中只包含有效敏感词，这里的level应该总是大于0
		// 但保留检查以防万一
		if level == 0 {
			continue
		}
		detail = append(detail, &bean.SensitiveMessagePlatformDetail{
			Keyword: originalWord, // 使用原始词作为关键词
			Level:   level,
		})
	}
	resp.PlatformDetail = detail

	if resp.ReplacedContent != "" {
		resp.ReplacedContent = l.trieService.CheckAndReplaceByCase(resp.ReplacedContent, ignoreCase)
		return resp, nil
	}
	resp.ReplacedContent = l.trieService.CheckAndReplaceByCase(req.Msg, ignoreCase)
	return resp, nil
}

func (l *SensitiveLogic) wechatSensitiveMessage(ctx context.Context, gameID, userID, msg string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}
	conf, err := l.minigameService.GetMinigameConfig(ctx, gameID)
	if err != nil {
		return nil, err
	}
	user, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		return nil, err
	}
	result, err := l.sensitiveService.VerifySensitiveMessage(ctx, user.OpenID, conf.AccessToken, msg)
	if err != nil {
		return nil, err
	}
	resp.TraceID = result.TraceID
	resp.ErrCode = result.ErrCode
	resp.ErrMsg = result.ErrMsg
	resp.Source = constants.PlatformTypeMinigame
	resp.Result = result.Result
	resp.Detail = result.Detail
	resp.ReplacedContent = result.Result.ReplacedContent
	return resp, nil
}

func (l *SensitiveLogic) douyinSensitiveMessage(ctx context.Context, gameID, msg string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}
	conf, err := l.douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		return nil, err
	}
	result, err := l.sensitiveService.VerifyDouyinSensitiveMessage(ctx, conf.AccessToken, msg)
	if err != nil {
		return nil, err
	}
	if len(result.Data) == 0 {
		return nil, fmt.Errorf("VerifyDouyinSensitiveMessage data len is zero, gameID: %s", gameID)
	}
	r := result.Data[0]
	resp.TraceID = result.LogID
	resp.ErrCode = r.Code
	resp.ErrMsg = r.Msg
	resp.Source = constants.PlatformTypeDouyin
	resp.Detail = r.Predicts
	resp.Result = l.handleDouyinSensitiveResult(ctx, msg, r.Predicts)
	if resp.Result.ReplacedContent != "" {
		resp.ReplacedContent = resp.Result.ReplacedContent
	}
	return resp, nil
}

func (l *SensitiveLogic) handleDouyinSensitiveResult(ctx context.Context, msg string, predicts []bean.DouyinSecurityPredict) bean.WechatSecurityCheckResult {
	// 遍历predicts，找到第一个hit为true的，然后返回
	// 检测结果-置信度-概率，值为 0 或者 1，当值为 1 时表示检测的文本包含违法违规内容
	for _, predict := range predicts {
		if predict.Prob == constants.SensitiveProbRisk {
			return bean.WechatSecurityCheckResult{
				Suggest:         constants.SensitiveSuggestRisk,
				Label:           constants.SensitiveLabelRisk,
				ReplacedContent: l.replaceSensitiveWord(msg),
			}
		}
	}
	return bean.WechatSecurityCheckResult{
		Suggest:         constants.SensitiveSuggestPass,
		Label:           constants.SensitiveLabelPass,
		ReplacedContent: msg,
	}
}

func (l *SensitiveLogic) replaceSensitiveWord(msg string) string {
	// 如果发现prob为1，则将msg中的蚊子全部替换为*
	charCount := utf8.RuneCountInString(msg)
	return strings.Repeat("*", charCount)
}
