package logic

import (
	"context"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_tencentCloudLogic *TencentCloudLogic
)

type TencentCloudLogic struct {
	tencentCloudService *service.TencentCloudService
	h5AdminUserService  *service.H5AdminAuthService
}

func SingletonTencentCloudLogic() *TencentCloudLogic {
	if _tencentCloudLogic == nil {
		_tencentCloudLogic = &TencentCloudLogic{
			tencentCloudService: service.SingletonTencentCloudService(),
			h5AdminUserService:  service.SingletonH5AdminAuthService(),
		}
	}
	return _tencentCloudLogic
}

// VerifyRealName 获取实名核身结果
func (l *TencentCloudLogic) VerifyRealName(ctx context.Context, req *bean.VerifyRealNameReq) (*bean.VerifyRealNameResp, error) {
	// 校验req.UserID在数据库中是否合法
	if err := l.h5AdminUserService.ValidateUserID(ctx, req.UserID); err != nil {
		return nil, err
	}

	code, msg, err := l.tencentCloudService.VerifyRealName(ctx, req)
	if err != nil {
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "VerifyRealName idcard: %s, code: %s, msg: %s", req.IDCard, code, msg)

	// 更新用户实名认证状态
	if err := l.h5AdminUserService.UpdateUserRealNameAuth(ctx, req.UserID, code); err != nil {
		logger.Logger.WarnfCtx(ctx, "更新用户实名认证状态失败: %v", err)
	}

	// 计算是否未成年并更新状态
	isMinors, err := l.h5AdminUserService.UpdateUserMinorsStatus(ctx, req.UserID, req.IDCard)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "更新用户未成年状态失败: %v", err)
	}

	return &bean.VerifyRealNameResp{
		IsRealName: code == "0",
		IsMinors:   isMinors,
	}, nil
}
